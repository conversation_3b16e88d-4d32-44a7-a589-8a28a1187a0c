/*!
 * @file
 * @brief This file defines public constants, types and functions for the
 *        stepper motor driver library.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __STEPPER_MOTOR_LIB_H__
#define __STEPPER_MOTOR_LIB_H__

#include <stdbool.h>
#include <stdint.h>

//---------------------------------------------------------
// Driver IC types Configuration Constants
//---------------------------------------------------------
#define U8_DRIVER_TYPES_LB1909M_4BEATS ((uint8_t)0)
#define U8_DRIVER_TYPES_LB1205M_4BEATS ((uint8_t)1)
#define U8_DRIVER_TYPES_LB1205M_8BEATS ((uint8_t)2)
#define U8_DRIVER_TYPES_MAX ((uint8_t)3)

#define U8_StepperMotorGoHomeDirection ((uint8_t)0)
#define U8_StepperMotorFarAwayDirection ((uint8_t)1)

#define U8_StepperMotorIdleState ((uint8_t)0)

// bit0 IO_En
#define U8_StepperMotorMask_IO_En ((uint8_t)1)
// bit1 IO_InA
#define U8_StepperMotorMask_IO_InA ((uint8_t)2)
// bit2 IO_InB
#define U8_StepperMotorMask_IO_InB ((uint8_t)4)
// bit3 IO_InA_
#define U8_StepperMotorMask_IO_InA_ ((uint8_t)8)
// bit4 IO_InB_
#define U8_StepperMotorMask_IO_InB_ ((uint8_t)16)

/*!
 * @brief 关键参数可在模块程序中配置，新增驱动芯片可继续扩展，不影响旧程序使用
 *
 */

typedef struct StepperMotorConstPara_st {
  uint16_t u16_StepperMotorStartDelayTimeMs;     ///< 两次动作间隔时间
  uint16_t u16_StepperMotorFirstLastPulseTimeMs; ///< 首末脉冲时间
  uint8_t u8_StepperMotorRunPPSTimeMs;           ///< 单步执行时间
  uint8_t u8_DriverTypes;                        ///< 驱动类型
} StepperMotorConstPara_st;

typedef void (*p_ImportStepperMotorIO)(const uint8_t u8_stepperMotorIOState);

struct StepperMotorDriver_API_st;

typedef struct StepperMotorDriver_st {
  const struct StepperMotorDriver_API_st *api;

  struct {
    p_ImportStepperMotorIO ImportStepperMotorIO; // 设定IO参数函数指针
    StepperMotorConstPara_st *pst_StepperMotorConstPara; // 设定宏定义参数

    uint16_t u16_TargetSteps;
    uint16_t u16_RunSteps;
    uint16_t u16_PPSTimer;
    uint8_t u8_StepState;
    uint8_t u8_Direction;
    bool b_IsInitialized;
    bool b_IsRunning;
    bool b_IsPausing;
    bool b_FirstPulse;
    bool b_LastPulse;
  } _private;

} StepperMotorDriver_st;

typedef struct StepperMotorDriver_API_st {
  /*!
   * @brief 步进电机驱动执行
   * @param stepper motor instance
   * @attention 必须放在1ms中断中执行
   * @note 必须放在1ms中断中执行
   */
  void (*pDrive_StepperMotorISR)(StepperMotorDriver_st *pst_stepperMotorInst);

  /*!
   * @brief 设置步进电机转动方向和步数
   * @param stepper motor instance
   * @param u16_tartgetSteps - 步进电机转动方向
   * @param u8_tartgetDirection - 步进电机转动步数
   */
  bool (*pSet_StepperMotorTartget)(StepperMotorDriver_st *pst_stepperMotorInst,
                                   uint16_t u16_tartgetSteps,
                                   uint8_t u8_tartgetDirection);

  /*!
   * @brief 强制停止步进电机
   * @param stepper motor instance
   */
  void (*pStop_StepperMotorAction)(StepperMotorDriver_st *pst_stepperMotorInst);

  /*!
   * @brief 暂停步进电机
   * @param stepper motor instance
   * @param b_pausingState
   */
  void (*pPause_StepperMotorAction)(StepperMotorDriver_st *pst_stepperMotorInst,
                                    bool b_pausingState);

  /*!
   * @brief 获取步进电机是否在运行的状态
   * @param stepper motor instance
   * @retval b_IsRunning - 1: 正在运行，0: 达到目标位置
   */
  bool (*pGet_StepperMotorIsRunning)(
      StepperMotorDriver_st *pst_stepperMotorInst);

  /*!
   * @brief 获取步进电机是否在暂停的状态
   * @param stepper motor instance
   * @retval b_IsPausing - 1: 正在暂停，0: 非暂停
   */
  bool (*pGet_StepperMotorIsPausing)(
      StepperMotorDriver_st *pst_stepperMotorInst);

  /*!
   * @brief 获取步进电机运行步数
   * @param stepper motor instance
   * @retval u16_RunSteps
   */
  uint16_t (*pGet_StepperMotorRunSteps)(
      StepperMotorDriver_st *pst_stepperMotorInst);

  /*!
   * @brief 获取步进电机剩余步数
   * @param stepper motor instance
   * @retval u16_TargetSteps - u16_RunSteps
   */
  uint16_t (*pGet_StepperMotorRemainingSteps)(
      StepperMotorDriver_st *pst_stepperMotorInst);

} StepperMotorDriver_API_st;

/*!
 * @brief 步进电机初始化
 * @pre All pointers != NULL
 * @param p_StepperMotorInst - 步进电机实例
 * @param p_ImportStepperMotorIO - 关联IO口配置
 * @param StepperMotorConstPara_st - 步进电机运行配置参数指针
 */
void Init_StepperMotor(StepperMotorDriver_st *pst_stepperMotorInst,
                       p_ImportStepperMotorIO importIO,
                       StepperMotorConstPara_st *pst_constPara);

#endif /* __STEPPER_MOTOR_LIB_H__ */
