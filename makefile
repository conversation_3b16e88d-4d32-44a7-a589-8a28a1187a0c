# Makefile for StepperMotorLib (multi-platform)

# Default platform (x86)
PLATFORM ?= x86

# Compiler and flags based on platform
ifeq ($(PLATFORM),arm)
    CC = arm-none-eabi-gcc
    AR = arm-none-eabi-ar
    CFLAGS = -mcpu=cortex-m0plus -mthumb -Wall -g -O0
    TARGET_DIR = arm_build
		BUILD_TESTS = 0
else
    CC = gcc
    AR = ar
    CFLAGS = -Wall -g -O0 -fPIC
    TARGET_DIR = x86_build
		BUILD_TESTS = 1
endif

INCLUDES = -I. -ICode/StepperMotor -ITests/StepperMotor

# Source files
SRCS = Code/StepperMotor/StepperMotorLib.c
OBJS = $(patsubst Code/StepperMotor/%.c,$(TARGET_DIR)/%.o,$(SRCS))
STATIC_LIB = $(TARGET_DIR)/libStepperMotor.a

# Add shared library for x86 only
ifeq ($(PLATFORM),x86)
    SHARED_LIB = $(TARGET_DIR)/libStepperMotor.so
    ALL_TARGETS = $(SHARED_LIB)
else
    ALL_TARGETS = $(STATIC_LIB)
endif

# Test related variables
TEST_SRC = Tests/AllTests.c Tests/StepperMotor/test_steppermotor.c
TEST_OBJ = $(TARGET_DIR)/AllTests.o $(TARGET_DIR)/test_steppermotor.o
TEST_BIN = $(TARGET_DIR)/test_stepper_motor

# Default target
all: $(TARGET_DIR) tests

# Create build directory
$(TARGET_DIR):
	mkdir -p $(TARGET_DIR)

# Compile source files
$(TARGET_DIR)/%.o: Code/StepperMotor/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Compile AllTests
$(TARGET_DIR)/AllTests.o: Tests/AllTests.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Compile test_cases.c
$(TARGET_DIR)/test_steppermotor.o: Tests/StepperMotor/test_steppermotor.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Create static library
$(STATIC_LIB): $(OBJS)
	$(AR) rcs $@ $(OBJS)
	ranlib $@

# Create shared library (x86 only)
$(SHARED_LIB): $(OBJS)
	$(CC) -shared -o $@ $(OBJS)


# Build tests (x86 only)
ifeq ($(BUILD_TESTS),1)
tests: $(TEST_BIN)

$(TEST_BIN): $(TEST_OBJ) $(STATIC_LIB)
	$(CC) -o $@ $(TEST_OBJ) $(STATIC_LIB) -lcmocka

run-tests: $(TEST_BIN)
	./$(TEST_BIN)
else
tests:
	@echo "Tests are only built for x86 platform"
endif

# Clean
clean:
	rm -rf x86_build arm_build

# Build for both platforms
all-platforms: clean
	$(MAKE) PLATFORM=x86
	$(MAKE) PLATFORM=arm

# Test compilation only
test-compile: $(SRCS)
	$(CC) $(CFLAGS) $(INCLUDES) -c $(SRCS) -o /dev/null

.PHONY: all clean test-compile all-platforms tests run-tests