#!/bin/bash

CONF_PATH=/home/<USER>/ext/Downloads/cov-analysis-linux64-2024.3.1/config/coding-standards/misrac2012/
COVERITY=/home/<USER>/coverity/
rm -rf  $COVERITY

cov-build --dir "$COVERITY/result" --tmpdir "$COVERITY/tmp" --encoding UTF-8 make

cov-analyze --dir "$COVERITY"/result --strip-path ~/work/fridgecommon/ --coding-standard-config $PATH/misrac2012-all.config --jobs max8

cov-commit-defects --host ************ --dataport 443 --on-new-cert trust --stream fctest --dir "$COVERITY"/result  --user dajiadian --password dajiadian123