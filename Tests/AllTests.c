#include "Tests/StepperMotor/test_steppermotor.h"
#include <cmocka.h>

int main(void) {
  const struct CMUnitTest tests[] = {
      cmocka_unit_test(test_init_stepper_motor),
      cmocka_unit_test(test_set_target),
      cmocka_unit_test(test_pause_resume),
      cmocka_unit_test(test_stop_action),
      cmocka_unit_test(test_get_running_status),
      cmocka_unit_test(test_get_steps_info),
      cmocka_unit_test(test_drive_stepper_motor_isr),
      cmocka_unit_test(test_different_driver_types),
  };

  return cmocka_run_group_tests(tests, NULL, NULL);
}