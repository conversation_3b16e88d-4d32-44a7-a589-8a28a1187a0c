#!/bin/bash

# CMocka源码下载和编译脚本
# 此脚本会自动下载CMocka源码并编译为静态库

set -e  # 遇到错误立即退出

CMOCKA_VERSION="1.1.7"
CMOCKA_URL="https://cmocka.org/files/1.1/cmocka-${CMOCKA_VERSION}.tar.xz"
CMOCKA_DIR="cmocka-${CMOCKA_VERSION}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${SCRIPT_DIR}/build"
INSTALL_DIR="${SCRIPT_DIR}/install"

echo "=== CMocka源码下载和编译脚本 ==="
echo "版本: ${CMOCKA_VERSION}"
echo "脚本目录: ${SCRIPT_DIR}"

# 清理旧的构建文件
if [ -d "${BUILD_DIR}" ]; then
    echo "清理旧的构建目录..."
    rm -rf "${BUILD_DIR}"
fi

if [ -d "${INSTALL_DIR}" ]; then
    echo "清理旧的安装目录..."
    rm -rf "${INSTALL_DIR}"
fi

# 创建构建目录
mkdir -p "${BUILD_DIR}"
cd "${BUILD_DIR}"

# 检查是否已经下载了源码
if [ ! -f "${CMOCKA_DIR}.tar.xz" ]; then
    echo "下载CMocka源码..."
    if command -v wget >/dev/null 2>&1; then
        wget "${CMOCKA_URL}"
    elif command -v curl >/dev/null 2>&1; then
        curl -L -O "${CMOCKA_URL}"
    else
        echo "错误: 需要wget或curl来下载源码"
        exit 1
    fi
else
    echo "CMocka源码已存在，跳过下载"
fi

# 解压源码
if [ ! -d "${CMOCKA_DIR}" ]; then
    echo "解压CMocka源码..."
    tar -xf "${CMOCKA_DIR}.tar.xz"
else
    echo "CMocka源码已解压，跳过解压"
fi

# 进入源码目录
cd "${CMOCKA_DIR}"

# 检查cmake是否可用
if ! command -v cmake >/dev/null 2>&1; then
    echo "错误: 需要cmake来构建CMocka"
    echo "请安装cmake: sudo apt-get install cmake"
    exit 1
fi

# 创建构建目录
mkdir -p build
cd build

# 配置CMake
echo "配置CMake..."
cmake .. \
    -DCMAKE_INSTALL_PREFIX="${INSTALL_DIR}" \
    -DCMAKE_BUILD_TYPE=Release \
    -DBUILD_SHARED_LIBS=OFF \
    -DWITH_CMOCKERY_SUPPORT=OFF \
    -DUNIT_TESTING=OFF \
    -DWITH_EXAMPLES=OFF

# 编译
echo "编译CMocka..."
make -j$(nproc)

# 安装到本地目录
echo "安装CMocka到本地目录..."
make install

echo "=== CMocka编译完成 ==="
echo "头文件位置: ${INSTALL_DIR}/include"
echo "静态库位置: ${INSTALL_DIR}/lib"
echo ""
echo "可以在makefile中使用以下路径:"
echo "CMOCKA_INCLUDE = -I${INSTALL_DIR}/include"
echo "CMOCKA_LIB = ${INSTALL_DIR}/lib/libcmocka.a"
