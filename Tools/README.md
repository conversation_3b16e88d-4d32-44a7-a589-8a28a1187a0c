# 预置工具链说明

## 目录结构

```
Tools/
├── README.md                     # 本说明文件
├── gcc-wrapper.sh               # GCC包装脚本
├── x86_64-linux-gnu-gcc-13      # GCC编译器
├── x86_64-linux-gnu-ar          # AR归档工具
├── x86_64-linux-gnu-ranlib      # ranlib工具
├── lib/                         # GCC库文件
│   └── gcc/
│       └── x86_64-linux-gnu/
│           └── 13/
│               ├── include/     # GCC内置头文件
│               ├── include-fixed/
│               └── *.o          # 编译时需要的对象文件
├── libexec/                     # GCC可执行组件
│   └── gcc/
│       └── x86_64-linux-gnu/
│           └── 13/
│               ├── cc1          # C编译器前端
│               ├── cc1plus      # C++编译器前端
│               ├── collect2     # 链接器包装
│               └── ...          # 其他组件
└── cmocka/                      # CMocka测试框架
    ├── include/
    └── lib/
```

## 工具链组件

### GCC编译器

- **文件**: `x86_64-linux-gnu-gcc-13`
- **版本**: GCC 13.1.0 (Ubuntu 13.1.0-8ubuntu1~22.04)
- **用途**: C/C++代码编译

### 包装脚本

- **文件**: `gcc-wrapper.sh`
- **用途**: 设置正确的环境变量，使GCC能找到其组件和系统头文件
- **功能**:
  - 设置 `GCC_EXEC_PREFIX`
  - 设置 `COMPILER_PATH`
  - 设置 `LIBRARY_PATH`
  - 添加系统头文件路径

### AR归档工具

- **文件**: `x86_64-linux-gnu-ar`
- **用途**: 创建和管理静态库(.a文件)

### ranlib工具

- **文件**: `x86_64-linux-gnu-ranlib`
- **用途**: 为静态库生成索引，提高链接速度

## 使用方法

工具链通过makefile自动使用，无需手动调用。makefile中的配置：

```makefile
CC = Tools/gcc-wrapper.sh
AR = Tools/x86_64-linux-gnu-ar
```

## 环境要求

预置工具链仍然依赖系统的一些基础组件：

1. **系统头文件**: `/usr/include/` 和 `/usr/include/x86_64-linux-gnu/`
2. **系统库**: `/usr/lib/x86_64-linux-gnu/` 中的基础库
3. **动态链接器**: `/lib64/ld-linux-x86-64.so.2`

## 优势

1. **版本一致性**: 所有开发者使用相同版本的GCC
2. **环境隔离**: 不依赖系统安装的特定GCC版本
3. **可移植性**: 可以在不同的Ubuntu/Debian系统上使用
4. **自包含**: 包含了编译所需的大部分组件

## 故障排除

### 编译失败

如果遇到编译错误：

1. 检查系统头文件是否存在：

   ```bash
   ls -la /usr/include/stdio.h
   ls -la /usr/include/x86_64-linux-gnu/
   ```
2. 检查工具链文件权限：

   ```bash
   ls -la Tools/gcc-wrapper.sh
   ls -la Tools/x86_64-linux-gnu-gcc-13
   ```
3. 测试包装脚本：

   ```bash
   Tools/gcc-wrapper.sh --version
   ```

### 链接失败

如果遇到链接错误：

1. 检查系统库是否存在：

   ```bash
   ls -la /usr/lib/x86_64-linux-gnu/libc.so
   ```
2. 检查动态链接器：

   ```bash
   ls -la /lib64/ld-linux-x86-64.so.2
   ```

## 更新工具链

要更新工具链到新版本：

1. 替换 `x86_64-linux-gnu-gcc-13` 文件
2. 更新 `lib/gcc/x86_64-linux-gnu/13/` 目录中的文件
3. 更新 `libexec/gcc/x86_64-linux-gnu/13/` 目录中的文件
4. 如果版本号变化，需要相应更新目录结构和包装脚本
