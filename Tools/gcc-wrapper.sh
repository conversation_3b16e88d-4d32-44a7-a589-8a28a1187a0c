#!/bin/bash

# GCC包装脚本
# 设置正确的环境变量以使用预置的gcc工具链

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置GCC相关的环境变量
export GCC_EXEC_PREFIX="${SCRIPT_DIR}/"
export COMPILER_PATH="${SCRIPT_DIR}/libexec/gcc/x86_64-linux-gnu/13:${SCRIPT_DIR}"
export LIBRARY_PATH="${SCRIPT_DIR}/lib/gcc/x86_64-linux-gnu/13"

# 添加系统头文件路径
SYSTEM_INCLUDES=(
    "-isystem" "/usr/include/x86_64-linux-gnu"
    "-isystem" "/usr/include"
    "-isystem" "${SCRIPT_DIR}/lib/gcc/x86_64-linux-gnu/13/include"
    "-isystem" "${SCRIPT_DIR}/lib/gcc/x86_64-linux-gnu/13/include-fixed"
)

# 执行预置的gcc，添加系统头文件路径
exec "${SCRIPT_DIR}/x86_64-linux-gnu-gcc-13" "${SYSTEM_INCLUDES[@]}" "$@"
